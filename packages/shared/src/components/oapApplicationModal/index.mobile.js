import { View, StyleSheet } from 'react-native'
import React, { useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Button, Text, Loader } from '@libs/components'
import { generateOapAuthPayload } from '../../utils'

const OapPortalMobile = ({
  portalUrl,
  messagePayload,
  onClose = () => {},
  opportunityDetails,
  userType = 'student',
  applicationStatus = 'new',
  userEmail,
  tokens,
  handleSubmissionConfirmation = () => {},
}) => {
  const { t } = useTranslation()
  const iframeRef = useRef(null)
  const [isLoading, setIsLoading] = useState(true) // Start with loading true
  const [isIframeLoading, setIsIframeLoading] = useState(true) // Track iframe loading
  const [errorMessage, setErrorMessage] = useState('')
  const [isIframeReady, setIsIframeReady] = useState(false)

  useEffect(() => {
    const sendTokensToIframe = async () => {
      try {
        setIsLoading(true)
        setIsIframeLoading(true)
        setIsIframeReady(false)

        localStorage.setItem(
          'redirect-to-oap',
          JSON.stringify({
            enable: true,
            oapName: messagePayload?.oapName,
          }),
        )

        // Get the iframe element
        const iframe = document.getElementById('oap-portal-iframe')
        if (!iframe) {
          setIsLoading(false)
          setIsIframeLoading(false)
          return
        }

        const targetOrigin = new URL(portalUrl).origin

        // Function to send authentication message to iframe
        const sendAuthMessage = () => {
          // Use the dynamic payload generation utility
          let message

          if (opportunityDetails && userEmail && tokens) {
            // Generate dynamic payload based on user type and status
            try {
              message = generateOapAuthPayload(
                opportunityDetails,
                userType,
                applicationStatus,
                tokens,
                userEmail,
              )
            } catch (error) {
              console.warn(
                'Failed to generate dynamic payload, falling back to messagePayload:',
                error,
              )
              message = messagePayload
            }
          }

          console.log('Sending message to OAP Portal:', targetOrigin)

          if (iframe && iframe.contentWindow) {
            iframe.contentWindow.postMessage(message, targetOrigin)
            setIsLoading(false)
            setIsIframeLoading(false)
          } else {
            setErrorMessage('Cannot access Oap Portal. Please try again.')
            setIsLoading(false)
            setIsIframeLoading(false)
          }
        }

        const messageHandler = (event) => {
          // Process messages from the iframe
          if (event.data && typeof event.data === 'object') {
            if (event.data.type === 'ready_state') {
              setIsIframeReady(true)
              sendAuthMessage()
            }
          }
        }

        window.addEventListener('message', messageHandler)

        // Add a load event listener to the iframe
        const iframeLoadHandler = () => {
          // Iframe has loaded, but we still need to wait for ready state or timeout
          setIsIframeLoading(false)

          const readyTimeout = setTimeout(() => {
            if (!isIframeReady) {
              sendAuthMessage()
            }
          }, 6000)

          iframe.readyTimeoutId = readyTimeout

          // Remove the event listener after it's been triggered
          iframe.removeEventListener('load', iframeLoadHandler)
        }

        // Add the load event listener to the iframe
        iframe.addEventListener('load', iframeLoadHandler)

        // If the iframe is already loaded, trigger the handler manually
        if (iframe.contentDocument) {
          iframeLoadHandler()
        }
      } catch (error) {
        setErrorMessage(`Failed to initialize Oap Portal: ${error.message}`)
        setIsLoading(false)
        setIsIframeLoading(false)
      }
    }

    sendTokensToIframe()

    return () => {
      const iframe = document.getElementById('oap-portal-iframe')
      if (iframe) {
        iframe.removeEventListener('load', () => {})
        // Clear timeout if it exists
        if (iframe.readyTimeoutId) {
          clearTimeout(iframe.readyTimeoutId)
        }
      }
      // Remove message listener
      window.removeEventListener('message', () => {})
    }
  }, [messagePayload, portalUrl, isIframeReady, tokens])

  useEffect(() => {
    // Set the iframe height to fill the container
    const iframe = document.getElementById('oap-portal-iframe')
    if (iframe.contentDocument) {
      window.addEventListener('message', (event) => {
        if (event.data.type === 'SUBMISSION_CONFIRMATION') {
          handleSubmissionConfirmation(event.data)
        }
      })
    }
  }, [])

  if (isLoading || isIframeLoading) {
    return (
      <View style={styles.container}>
        <View style={styles.loadingContainer}>
          <Loader size="large" colorVariant="primary" />
          <Text style={styles.loadingText}>
            {isIframeLoading ? 'Loading application...' : 'Initializing...'}
          </Text>
        </View>
      </View>
    )
  }

  if (errorMessage) {
    return (
      <View style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{errorMessage}</Text>
          <Button
            label={t('Oap_PORTAL.CLOSE', 'Close')}
            onPress={onClose}
            buttonStyle={styles.errorButton}
          />
        </View>
      </View>
    )
  }

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <View style={styles.iframeContainer}>
          <iframe
            id="oap-portal-iframe"
            ref={iframeRef}
            src={`${portalUrl}?fromAppHero=true`}
            style={styles.iframe}
            title="Oap Portal"
          />
        </View>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    minHeight: '100vh',
  },
  content: {
    flex: 1,
  },
  iframeContainer: {
    flex: 1,
    backgroundColor: '#ffffff',
    borderRadius: 12,
    overflow: 'hidden',
    minHeight: '596px',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  iframe: {
    width: '100%',
    height: '100%',
    border: 'none',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderRadius: 12,
    minHeight: '70vh',
    marginHorizontal: 24,
    marginVertical: 16,
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 24,
    marginHorizontal: 24,
    marginVertical: 16,
    minHeight: '70vh',
  },
  errorText: {
    fontSize: 16,
    color: '#E53E3E',
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 24,
  },
  errorButton: {
    minWidth: 120,
    paddingHorizontal: 24,
  },
})

export default OapPortalMobile
